@echo off
echo Building SimpleJson Test...

REM 简单编译测试（不依赖MySQL）
g++ -std=c++17 -I"include" src\main.cpp -o simple_test.exe

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Running test...
    simple_test.exe
) else (
    echo Build failed! Trying with Visual Studio compiler...

    REM 尝试使用Visual Studio编译器
    cl /EHsc /std:c++17 /I"include" src\main.cpp /Fe:simple_test_vs.exe

    if %ERRORLEVEL% EQU 0 (
        echo VS Build successful!
        simple_test_vs.exe
    ) else (
        echo Both builds failed!
    )
)

pause
