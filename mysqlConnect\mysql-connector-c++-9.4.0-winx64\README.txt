Copyright (c) 2008, 2025, Oracle and/or its affiliates.

This is a release of MySQL Connector/C++, the C++ interface for communicating
with MySQL servers.

License information can be found in the LICENSE.txt file.

This distribution may include materials developed by third parties.
For license and attribution notices for these materials, please refer to the LICENSE.txt file.

For more information on MySQL Connector/C++, visit
  https://dev.mysql.com/doc/connector-cpp/en/

For additional downloads and the source of MySQL Connector/C++, visit
  http://dev.mysql.com/downloads

MySQL Connector/C++ is brought to you by the MySQL team at Oracle.


DOCUMENTATION LOCATION
======================

You can find the documentation on the MySQL website at
<http://dev.mysql.com/doc/dev/connector-cpp/>

For the new features/bugfix history, see release notes at
<https://dev.mysql.com/doc/relnotes/connector-cpp/en/news-8-0.html>.
Note that the initial releases used major version 2.0.

CONTACT
=======

For general discussion of the MySQL Connector/C++ please use the C/C++
community forum at <http://forums.mysql.com/list.php?167> or join
the MySQL Connector/C++ mailing list at <http://lists.mysql.com>.

Bugs can be reported at <http://bugs.mysql.com/report.php>. Please
use the "Connector / C++" or "Connector / C++ Documentation" bug
category.
