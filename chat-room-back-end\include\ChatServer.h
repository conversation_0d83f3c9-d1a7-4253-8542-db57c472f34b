#pragma once
#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <thread>
#include <mutex>
#include <memory>
#include <winsock2.h>
#include <ws2tcpip.h>
#include "SimpleJson.h"
#include "DbConnect.h"

#pragma comment(lib, "ws2_32.lib")

using namespace std;

// 消息类型枚举
enum class MessageType {
    LOGIN = 1,
    REGISTER = 2,
    CHAT_MESSAGE = 3,
    USER_LIST = 4,
    USER_JOIN = 5,
    USER_LEAVE = 6,
    HISTORY_REQUEST = 7,
    HISTORY_RESPONSE = 8,
    ERROR_MESSAGE = 9,
    HEARTBEAT = 10
};

// 客户端信息结构
struct ClientInfo {
    SOCKET socket;
    string username;
    string userId;
    bool isAuthenticated;
    thread clientThread;
    
    ClientInfo(SOCKET s) : socket(s), isAuthenticated(false) {}
};

// 消息结构
struct Message {
    MessageType type;
    string senderId;
    string senderName;
    string content;
    string timestamp;
    
    Message() = default;
    Message(MessageType t, const string& id, const string& name, const string& msg)
        : type(t), senderId(id), senderName(name), content(msg) {}
};

class ChatServer {
private:
    SOCKET serverSocket;
    int port;
    bool isRunning;
    
    // 客户端管理
    map<SOCKET, shared_ptr<ClientInfo>> clients;
    mutex clientsMutex;
    
    // 数据库连接
    shared_ptr<DbConnect> dbConnection;
    
    // 消息历史
    vector<Message> messageHistory;
    mutex historyMutex;
    
    // 私有方法
    bool InitializeWinsock();
    bool CreateServerSocket();
    bool BindAndListen();
    void AcceptClients();
    void HandleClient(shared_ptr<ClientInfo> client);
    
    // 消息处理
    void ProcessMessage(shared_ptr<ClientInfo> client, const string& rawMessage);
    void HandleLogin(shared_ptr<ClientInfo> client, const Json::Value& message);
    void HandleRegister(shared_ptr<ClientInfo> client, const Json::Value& message);
    void HandleChatMessage(shared_ptr<ClientInfo> client, const Json::Value& message);
    void HandleHistoryRequest(shared_ptr<ClientInfo> client, const Json::Value& message);
    
    // 广播和发送
    void BroadcastMessage(const Message& message, SOCKET excludeSocket = INVALID_SOCKET);
    void SendToClient(SOCKET clientSocket, const Json::Value& message);
    void SendUserList(SOCKET clientSocket);
    void SendErrorMessage(SOCKET clientSocket, const string& error);
    
    // 用户管理
    bool AuthenticateUser(const string& username, const string& password, string& userId);
    bool RegisterUser(const string& username, const string& password, const string& email);
    void RemoveClient(SOCKET clientSocket);
    void NotifyUserJoin(const string& username);
    void NotifyUserLeave(const string& username);
    
    // 数据库操作
    void SaveMessageToDatabase(const Message& message);
    vector<Message> LoadMessageHistory(int limit = 50);
    
    // 工具方法
    string GetCurrentTimestamp();
    Json::Value MessageToJson(const Message& message);
    Message JsonToMessage(const Json::Value& json);
    
public:
    ChatServer(int serverPort = 8080);
    ~ChatServer();
    
    bool Start();
    void Stop();
    void Run();
    
    // 获取服务器状态
    bool IsRunning() const { return isRunning; }
    int GetClientCount() const;
    vector<string> GetConnectedUsers() const;
};
