#pragma once
#include <string>
#include <map>
#include <vector>
#include <sstream>
#include <iostream>
#include <cctype>
#include <stdexcept>

using namespace std;

// 简单的JSON处理类，用于替代jsoncpp
class SimpleJson {
public:
    enum ValueType {
        NULL_VALUE,
        INT_VALUE,
        STRING_VALUE,
        BOOL_VALUE,
        ARRAY_VALUE,
        OBJECT_VALUE
    };

private:
    ValueType type;
    int intValue;
    string stringValue;
    bool boolValue;
    vector<SimpleJson> arrayValue;
    map<string, SimpleJson> objectValue;

public:
    // 构造函数
    SimpleJson() : type(NULL_VALUE), intValue(0), boolValue(false) {}
    SimpleJson(int value) : type(INT_VALUE), intValue(value), boolValue(false) {}
    SimpleJson(const string& value) : type(STRING_VALUE), stringValue(value), intValue(0), boolValue(false) {}
    SimpleJson(const char* value) : type(STRING_VALUE), stringValue(value), intValue(0), boolValue(false) {}
    SimpleJson(bool value) : type(BOOL_VALUE), boolValue(value), intValue(0) {}

    // 类型检查
    bool isNull() const { return type == NULL_VALUE; }
    bool isInt() const { return type == INT_VALUE; }
    bool isString() const { return type == STRING_VALUE; }
    bool isBool() const { return type == BOOL_VALUE; }
    bool isArray() const { return type == ARRAY_VALUE; }
    bool isObject() const { return type == OBJECT_VALUE; }

    // 值获取
    int asInt() const { 
        if (type == INT_VALUE) return intValue;
        if (type == STRING_VALUE) {
            try { return stoi(stringValue); }
            catch (...) { return 0; }
        }
        return 0; 
    }
    
    string asString() const { 
        if (type == STRING_VALUE) return stringValue;
        if (type == INT_VALUE) return to_string(intValue);
        if (type == BOOL_VALUE) return boolValue ? "true" : "false";
        return ""; 
    }
    
    bool asBool() const { 
        if (type == BOOL_VALUE) return boolValue;
        if (type == INT_VALUE) return intValue != 0;
        if (type == STRING_VALUE) return !stringValue.empty();
        return false; 
    }

    // 对象访问
    SimpleJson& operator[](const string& key) {
        if (type != OBJECT_VALUE) {
            type = OBJECT_VALUE;
            objectValue.clear();
        }
        return objectValue[key];
    }

    const SimpleJson& operator[](const string& key) const {
        static SimpleJson nullValue;
        if (type == OBJECT_VALUE) {
            auto it = objectValue.find(key);
            if (it != objectValue.end()) {
                return it->second;
            }
        }
        return nullValue;
    }

    // 数组访问
    SimpleJson& operator[](int index) {
        if (type != ARRAY_VALUE) {
            type = ARRAY_VALUE;
            arrayValue.clear();
        }
        if (index >= static_cast<int>(arrayValue.size())) {
            arrayValue.resize(index + 1);
        }
        return arrayValue[index];
    }

    void append(const SimpleJson& value) {
        if (type != ARRAY_VALUE) {
            type = ARRAY_VALUE;
            arrayValue.clear();
        }
        arrayValue.push_back(value);
    }

    size_t size() const {
        if (type == ARRAY_VALUE) return arrayValue.size();
        if (type == OBJECT_VALUE) return objectValue.size();
        return 0;
    }

    // 序列化为JSON字符串
    string toString() const {
        stringstream ss;
        switch (type) {
            case NULL_VALUE:
                ss << "null";
                break;
            case INT_VALUE:
                ss << intValue;
                break;
            case STRING_VALUE:
                ss << "\"" << escapeString(stringValue) << "\"";
                break;
            case BOOL_VALUE:
                ss << (boolValue ? "true" : "false");
                break;
            case ARRAY_VALUE:
                ss << "[";
                for (size_t i = 0; i < arrayValue.size(); ++i) {
                    if (i > 0) ss << ",";
                    ss << arrayValue[i].toString();
                }
                ss << "]";
                break;
            case OBJECT_VALUE:
                {
                    ss << "{";
                    bool first = true;
                    for (const auto& pair : objectValue) {
                        if (!first) ss << ",";
                        ss << "\"" << escapeString(pair.first) << "\":" << pair.second.toString();
                        first = false;
                    }
                    ss << "}";
                }
                break;
        }
        return ss.str();
    }

    // 从JSON字符串解析
    static SimpleJson parse(const string& jsonStr) {
        size_t pos = 0;
        return parseValue(jsonStr, pos);
    }

private:
    static string escapeString(const string& str) {
        string result;
        for (char c : str) {
            switch (c) {
                case '"': result += "\\\""; break;
                case '\\': result += "\\\\"; break;
                case '\n': result += "\\n"; break;
                case '\r': result += "\\r"; break;
                case '\t': result += "\\t"; break;
                default: result += c; break;
            }
        }
        return result;
    }

    static void skipWhitespace(const string& str, size_t& pos) {
        while (pos < str.length() && (str[pos] == ' ' || str[pos] == '\t' || str[pos] == '\n' || str[pos] == '\r')) {
            pos++;
        }
    }

    static SimpleJson parseValue(const string& str, size_t& pos) {
        skipWhitespace(str, pos);
        if (pos >= str.length()) return SimpleJson();
        
        char c = str[pos];
        if (c == '"') {
            return parseString(str, pos);
        } else if (c == '{') {
            return parseObject(str, pos);
        } else if (c == '[') {
            return parseArray(str, pos);
        } else if (c == 't' || c == 'f') {
            return parseBool(str, pos);
        } else if (c == 'n') {
            return parseNull(str, pos);
        } else if (c == '-' || isdigit(c)) {
            return parseNumber(str, pos);
        }
        
        return SimpleJson();
    }

    static SimpleJson parseString(const string& str, size_t& pos) {
        if (pos >= str.length() || str[pos] != '"') return SimpleJson();
        pos++; // skip opening quote
        
        string result;
        while (pos < str.length() && str[pos] != '"') {
            if (str[pos] == '\\' && pos + 1 < str.length()) {
                pos++;
                switch (str[pos]) {
                    case '"': result += '"'; break;
                    case '\\': result += '\\'; break;
                    case 'n': result += '\n'; break;
                    case 'r': result += '\r'; break;
                    case 't': result += '\t'; break;
                    default: result += str[pos]; break;
                }
            } else {
                result += str[pos];
            }
            pos++;
        }
        
        if (pos < str.length()) pos++; // skip closing quote
        return SimpleJson(result);
    }

    static SimpleJson parseNumber(const string& str, size_t& pos) {
        size_t start = pos;
        if (pos < str.length() && str[pos] == '-') pos++;
        
        while (pos < str.length() && isdigit(str[pos])) {
            pos++;
        }
        
        if (pos > start) {
            string numStr = str.substr(start, pos - start);
            try {
                return SimpleJson(stoi(numStr));
            } catch (...) {
                return SimpleJson();
            }
        }
        return SimpleJson();
    }

    static SimpleJson parseBool(const string& str, size_t& pos) {
        if (pos + 4 <= str.length() && str.substr(pos, 4) == "true") {
            pos += 4;
            return SimpleJson(true);
        } else if (pos + 5 <= str.length() && str.substr(pos, 5) == "false") {
            pos += 5;
            return SimpleJson(false);
        }
        return SimpleJson();
    }

    static SimpleJson parseNull(const string& str, size_t& pos) {
        if (pos + 4 <= str.length() && str.substr(pos, 4) == "null") {
            pos += 4;
        }
        return SimpleJson();
    }

    static SimpleJson parseObject(const string& str, size_t& pos) {
        SimpleJson obj;
        obj.type = OBJECT_VALUE;

        if (pos >= str.length() || str[pos] != '{') return obj;
        pos++; // skip opening brace

        skipWhitespace(str, pos);
        if (pos < str.length() && str[pos] == '}') {
            pos++;
            return obj;
        }

        while (pos < str.length()) {
            skipWhitespace(str, pos);

            // Parse key
            SimpleJson key = parseString(str, pos);
            if (!key.isString()) break;

            skipWhitespace(str, pos);
            if (pos >= str.length() || str[pos] != ':') break;
            pos++; // skip colon

            // Parse value
            SimpleJson value = parseValue(str, pos);
            obj.objectValue[key.asString()] = value;

            skipWhitespace(str, pos);
            if (pos >= str.length()) break;

            if (str[pos] == '}') {
                pos++;
                break;
            } else if (str[pos] == ',') {
                pos++;
            } else {
                break;
            }
        }

        return obj;
    }

    static SimpleJson parseArray(const string& str, size_t& pos) {
        SimpleJson arr;
        arr.type = ARRAY_VALUE;

        if (pos >= str.length() || str[pos] != '[') return arr;
        pos++; // skip opening bracket

        skipWhitespace(str, pos);
        if (pos < str.length() && str[pos] == ']') {
            pos++;
            return arr;
        }

        while (pos < str.length()) {
            SimpleJson value = parseValue(str, pos);
            arr.arrayValue.push_back(value);

            skipWhitespace(str, pos);
            if (pos >= str.length()) break;

            if (str[pos] == ']') {
                pos++;
                break;
            } else if (str[pos] == ',') {
                pos++;
            } else {
                break;
            }
        }

        return arr;
    }
};

// 为了兼容性，创建别名
namespace Json {
    using Value = SimpleJson;

    class Reader {
    public:
        bool parse(const string& document, Value& root) {
            try {
                root = SimpleJson::parse(document);
                return true;
            } catch (...) {
                return false;
            }
        }
    };

    class StreamWriterBuilder {
    private:
        map<string, string> settings;
    public:
        StreamWriterBuilder& operator[](const string& key) {
            return *this;
        }

        StreamWriterBuilder& operator=(const string& value) {
            return *this;
        }
    };

    string writeString(const StreamWriterBuilder& builder, const Value& value) {
        return value.toString();
    }
}
