#pragma once
#include <string>
#include <map>
#include <vector>
#include <sstream>
#include <iostream>
#include <cctype>
#include <stdexcept>

// Simple JSON processing class to replace jsoncpp
class SimpleJson {
public:
    enum ValueType {
        JSON_NULL,
        JSON_INT,
        JSON_STRING,
        JSON_BOOL,
        JSON_ARRAY,
        JSON_OBJECT
    };

private:
    ValueType type;
    int intValue;
    std::string stringValue;
    bool boolValue;
    std::vector<SimpleJson> arrayValue;
    std::map<std::string, SimpleJson> objectValue;

public:
    // Constructors
    SimpleJson() : type(JSON_NULL), intValue(0), boolValue(false) {}
    SimpleJson(int value) : type(JSON_INT), intValue(value), boolValue(false) {}
    SimpleJson(const std::string& value) : type(JSON_STRING), stringValue(value), intValue(0), boolValue(false) {}
    SimpleJson(const char* value) : type(JSON_STRING), stringValue(value), intValue(0), boolValue(false) {}
    SimpleJson(bool value) : type(JSON_BOOL), boolValue(value), intValue(0) {}

    // Type checking
    bool isNull() const { return type == JSON_NULL; }
    bool isInt() const { return type == JSON_INT; }
    bool isString() const { return type == JSON_STRING; }
    bool isBool() const { return type == JSON_BOOL; }
    bool isArray() const { return type == JSON_ARRAY; }
    bool isObject() const { return type == JSON_OBJECT; }

    // Value getters
    int asInt() const {
        if (type == JSON_INT) return intValue;
        if (type == JSON_STRING) {
            try { return std::stoi(stringValue); }
            catch (...) { return 0; }
        }
        return 0;
    }

    std::string asString() const {
        if (type == JSON_STRING) return stringValue;
        if (type == JSON_INT) return std::to_string(intValue);
        if (type == JSON_BOOL) return boolValue ? "true" : "false";
        return "";
    }

    bool asBool() const {
        if (type == JSON_BOOL) return boolValue;
        if (type == JSON_INT) return intValue != 0;
        if (type == JSON_STRING) return !stringValue.empty();
        return false;
    }

    // Object access
    SimpleJson& operator[](const std::string& key) {
        if (type != JSON_OBJECT) {
            type = JSON_OBJECT;
            objectValue.clear();
        }
        return objectValue[key];
    }

    const SimpleJson& operator[](const std::string& key) const {
        static SimpleJson nullValue;
        if (type == JSON_OBJECT) {
            auto it = objectValue.find(key);
            if (it != objectValue.end()) {
                return it->second;
            }
        }
        return nullValue;
    }

    // Array access
    SimpleJson& operator[](int index) {
        if (type != JSON_ARRAY) {
            type = JSON_ARRAY;
            arrayValue.clear();
        }
        if (index >= static_cast<int>(arrayValue.size())) {
            arrayValue.resize(index + 1);
        }
        return arrayValue[index];
    }

    void append(const SimpleJson& value) {
        if (type != JSON_ARRAY) {
            type = JSON_ARRAY;
            arrayValue.clear();
        }
        arrayValue.push_back(value);
    }

    size_t size() const {
        if (type == JSON_ARRAY) return arrayValue.size();
        if (type == JSON_OBJECT) return objectValue.size();
        return 0;
    }

    // Serialize to JSON string
    std::string toString() const {
        std::stringstream ss;
        switch (type) {
        case JSON_NULL:
            ss << "null";
            break;
        case JSON_INT:
            ss << intValue;
            break;
        case JSON_STRING:
            ss << "\"" << escapeString(stringValue) << "\"";
            break;
        case JSON_BOOL:
            ss << (boolValue ? "true" : "false");
            break;
        case JSON_ARRAY:
            ss << "[";
            for (size_t i = 0; i < arrayValue.size(); ++i) {
                if (i > 0) ss << ",";
                ss << arrayValue[i].toString();
            }
            ss << "]";
            break;
        case JSON_OBJECT:
        {
            ss << "{";
            bool first = true;
            for (const auto& pair : objectValue) {
                if (!first) ss << ",";
                ss << "\"" << escapeString(pair.first) << "\":" << pair.second.toString();
                first = false;
            }
            ss << "}";
        }
        break;
        }
        return ss.str();
    }

    // Parse from JSON string
    static SimpleJson parse(const std::string& jsonStr) {
        size_t pos = 0;
        return parseValue(jsonStr, pos);
    }

private:
    static std::string escapeString(const std::string& str) {
        std::string result;
        for (char c : str) {
            switch (c) {
            case '"': result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            default: result += c; break;
            }
        }
        return result;
    }

    static void skipWhitespace(const std::string& str, size_t& pos) {
        while (pos < str.length() && (str[pos] == ' ' || str[pos] == '\t' || str[pos] == '\n' || str[pos] == '\r')) {
            pos++;
        }
    }

    static SimpleJson parseValue(const std::string& str, size_t& pos) {
        skipWhitespace(str, pos);
        if (pos >= str.length()) return SimpleJson();

        char c = str[pos];
        if (c == '"') return parseString(str, pos);
        if (c == '{') return parseObject(str, pos);
        if (c == '[') return parseArray(str, pos);
        if (c == 't' || c == 'f') return parseBool(str, pos);
        if (c == 'n') return parseNull(str, pos);
        if (c == '-' || std::isdigit(c)) return parseNumber(str, pos);

        return SimpleJson();
    }

    static SimpleJson parseString(const std::string& str, size_t& pos) {
        if (pos >= str.length() || str[pos] != '"') return SimpleJson();
        pos++; // skip opening quote

        std::string result;
        while (pos < str.length() && str[pos] != '"') {
            if (str[pos] == '\\' && pos + 1 < str.length()) {
                pos++;
                switch (str[pos]) {
                case '"': result += '"'; break;
                case '\\': result += '\\'; break;
                case 'n': result += '\n'; break;
                case 'r': result += '\r'; break;
                case 't': result += '\t'; break;
                default: result += str[pos]; break;
                }
            } else {
                result += str[pos];
            }
            pos++;
        }
        if (pos < str.length()) pos++; // skip closing quote
        return SimpleJson(result);
    }

    static SimpleJson parseNumber(const std::string& str, size_t& pos) {
        size_t start = pos;
        if (pos < str.length() && str[pos] == '-') pos++;
        while (pos < str.length() && std::isdigit(str[pos])) pos++;

        std::string numStr = str.substr(start, pos - start);
        try {
            return SimpleJson(std::stoi(numStr));
        } catch (...) {
            return SimpleJson();
        }
    }

    static SimpleJson parseBool(const std::string& str, size_t& pos) {
        if (pos + 4 <= str.length() && str.substr(pos, 4) == "true") {
            pos += 4;
            return SimpleJson(true);
        }
        if (pos + 5 <= str.length() && str.substr(pos, 5) == "false") {
            pos += 5;
            return SimpleJson(false);
        }
        return SimpleJson();
    }

    static SimpleJson parseNull(const std::string& str, size_t& pos) {
        if (pos + 4 <= str.length() && str.substr(pos, 4) == "null") {
            pos += 4;
            return SimpleJson();
        }
        return SimpleJson();
    }

    static SimpleJson parseObject(const std::string& str, size_t& pos) {
        if (pos >= str.length() || str[pos] != '{') return SimpleJson();
        pos++; // skip opening brace

        SimpleJson result;
        result.type = JSON_OBJECT;

        skipWhitespace(str, pos);
        if (pos < str.length() && str[pos] == '}') {
            pos++;
            return result;
        }

        while (pos < str.length()) {
            skipWhitespace(str, pos);
            SimpleJson key = parseString(str, pos);
            if (!key.isString()) break;

            skipWhitespace(str, pos);
            if (pos >= str.length() || str[pos] != ':') break;
            pos++; // skip colon

            SimpleJson value = parseValue(str, pos);
            result.objectValue[key.asString()] = value;

            skipWhitespace(str, pos);
            if (pos >= str.length()) break;
            if (str[pos] == '}') {
                pos++;
                break;
            }
            if (str[pos] == ',') {
                pos++;
                continue;
            }
            break;
        }
        return result;
    }

    static SimpleJson parseArray(const std::string& str, size_t& pos) {
        if (pos >= str.length() || str[pos] != '[') return SimpleJson();
        pos++; // skip opening bracket

        SimpleJson result;
        result.type = JSON_ARRAY;

        skipWhitespace(str, pos);
        if (pos < str.length() && str[pos] == ']') {
            pos++;
            return result;
        }

        while (pos < str.length()) {
            SimpleJson value = parseValue(str, pos);
            result.arrayValue.push_back(value);

            skipWhitespace(str, pos);
            if (pos >= str.length()) break;
            if (str[pos] == ']') {
                pos++;
                break;
            }
            if (str[pos] == ',') {
                pos++;
                continue;
            }
            break;
        }
        return result;
    }
};

// jsoncpp compatibility namespace
namespace Json {
    using Value = SimpleJson;
}
