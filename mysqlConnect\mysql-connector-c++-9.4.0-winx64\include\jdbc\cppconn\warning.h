/*
 * Copyright (c) 2008, 2024, Oracle and/or its affiliates.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License, version 2.0, as
 * published by the Free Software Foundation.
 *
 * This program is designed to work with certain software (including
 * but not limited to OpenSSL) that is licensed under separate terms, as
 * designated in a particular file or component or in included license
 * documentation. The authors of MySQL hereby grant you an additional
 * permission to link the program and your derivative works with the
 * separately licensed software that they have either included with
 * the program or referenced in the documentation.
 *
 * Without limiting anything contained in the foregoing, this file,
 * which is part of Connector/C++, is also subject to the
 * Universal FOSS Exception, version 1.0, a copy of which can be found at
 * https://oss.oracle.com/licenses/universal-foss-exception.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 * See the GNU General Public License, version 2.0, for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA
 */



#ifndef _SQL_WARNING_H_
#define _SQL_WARNING_H_


#include <stdexcept>
#include <string>
#include <memory>
#include "sqlstring.h"

namespace sql
{

#ifdef _WIN32
#pragma warning (disable : 4290)
//warning C4290: C++ exception specification ignored except to indicate a function is not __declspec(nothrow)
#endif

class SQLWarning
{
public:

  SQLWarning(){}

  virtual const sql::SQLString & getMessage() const = 0;

  virtual const sql::SQLString & getSQLState() const = 0;

  virtual int getErrorCode() const = 0;

  virtual const SQLWarning * getNextWarning() const = 0;

  virtual void setNextWarning(const SQLWarning * _next) = 0;

protected:

  virtual ~SQLWarning(){};

  SQLWarning(const SQLWarning&){};

private:
  const SQLWarning & operator = (const SQLWarning & rhs);

};


} /* namespace sql */

#endif /* _SQL_WARNING_H_ */
