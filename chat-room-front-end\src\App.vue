<script setup>
import { ref, onMounted } from 'vue'
import LoginForm from './components/LoginForm.vue'
import ChatRoom from './components/ChatRoom.vue'

const isAuthenticated = ref(false)
const currentUser = ref(null)

const handleLogin = (userData) => {
  isAuthenticated.value = true
  currentUser.value = userData
}

const handleLogout = () => {
  isAuthenticated.value = false
  currentUser.value = null
}
</script>

<template>
  <div id="app">
    <header class="app-header">
      <h1>TCP Chat Room</h1>
      <div v-if="isAuthenticated" class="user-info">
        <span>Welcome, {{ currentUser?.username }}!</span>
        <button @click="handleLogout" class="logout-btn">Logout</button>
      </div>
    </header>

    <main class="app-main">
      <LoginForm
        v-if="!isAuthenticated"
        @login="handleLogin"
      />
      <ChatRoom
        v-else
        :user="currentUser"
        @logout="handleLogout"
      />
    </main>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.app-header h1 {
  color: white;
  font-size: 1.8rem;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.app-main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}
</style>
