#include "../include/ChatServer.h"
#include <sstream>
#include <iomanip>
#include <chrono>
#include <algorithm>
#include <ctime>

ChatServer::ChatServer(int serverPort) : port(serverPort), isRunning(false), serverSocket(INVALID_SOCKET) {
    dbConnection = make_shared<DbConnect>();
    messageHistory = LoadMessageHistory();
}

ChatServer::~ChatServer() {
    Stop();
}

bool ChatServer::InitializeWinsock() {
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        cerr << "WSAStartup failed: " << result << endl;
        return false;
    }
    return true;
}

bool ChatServer::CreateServerSocket() {
    serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (serverSocket == INVALID_SOCKET) {
        cerr << "Socket creation failed: " << WSAGetLastError() << endl;
        WSACleanup();
        return false;
    }
    
    // 设置socket选项，允许地址重用
    int opt = 1;
    if (setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt)) < 0) {
        cerr << "Setsockopt failed" << endl;
    }
    
    return true;
}

bool ChatServer::BindAndListen() {
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(port);
    
    if (bind(serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        cerr << "Bind failed: " << WSAGetLastError() << endl;
        closesocket(serverSocket);
        WSACleanup();
        return false;
    }
    
    if (listen(serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        cerr << "Listen failed: " << WSAGetLastError() << endl;
        closesocket(serverSocket);
        WSACleanup();
        return false;
    }
    
    return true;
}

bool ChatServer::Start() {
    if (isRunning) {
        cout << "Server is already running" << endl;
        return false;
    }
    
    if (!InitializeWinsock()) {
        return false;
    }
    
    if (!CreateServerSocket()) {
        return false;
    }
    
    if (!BindAndListen()) {
        return false;
    }
    
    isRunning = true;
    cout << "Chat server started on port " << port << endl;
    return true;
}

void ChatServer::Run() {
    if (!isRunning) {
        cout << "Server is not running. Call Start() first." << endl;
        return;
    }
    
    AcceptClients();
}

void ChatServer::AcceptClients() {
    while (isRunning) {
        sockaddr_in clientAddr;
        int clientAddrSize = sizeof(clientAddr);
        
        SOCKET clientSocket = accept(serverSocket, (sockaddr*)&clientAddr, &clientAddrSize);
        if (clientSocket == INVALID_SOCKET) {
            if (isRunning) {
                cerr << "Accept failed: " << WSAGetLastError() << endl;
            }
            continue;
        }
        
        // 创建客户端信息
        auto client = make_shared<ClientInfo>(clientSocket);
        
        // 获取客户端IP地址
        char clientIP[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
        
        cout << "New client connected from " << clientIP << ":" << ntohs(clientAddr.sin_port) << endl;
        
        // 添加到客户端列表
        {
            lock_guard<mutex> lock(clientsMutex);
            clients[clientSocket] = client;
        }
        
        // 创建处理线程
        client->clientThread = thread(&ChatServer::HandleClient, this, client);
        client->clientThread.detach();
    }
}

void ChatServer::HandleClient(shared_ptr<ClientInfo> client) {
    char buffer[4096];
    
    while (isRunning) {
        int bytesReceived = recv(client->socket, buffer, sizeof(buffer) - 1, 0);
        
        if (bytesReceived <= 0) {
            // 客户端断开连接
            cout << "Client disconnected: " << client->username << endl;
            if (client->isAuthenticated) {
                NotifyUserLeave(client->username);
            }
            RemoveClient(client->socket);
            break;
        }
        
        buffer[bytesReceived] = '\0';
        string rawMessage(buffer);
        
        // 处理可能的多个消息（以换行符分隔）
        stringstream ss(rawMessage);
        string line;
        while (getline(ss, line)) {
            if (!line.empty()) {
                ProcessMessage(client, line);
            }
        }
    }
}

void ChatServer::ProcessMessage(shared_ptr<ClientInfo> client, const string& rawMessage) {
    try {
        SimpleJson root = SimpleJson::parse(rawMessage);
        
        if (root.isNull()) {
            SendErrorMessage(client->socket, "Invalid JSON format");
            return;
        }
        
        if (!root.isMember("type")) {
            SendErrorMessage(client->socket, "Missing message type");
            return;
        }
        
        MessageType type = static_cast<MessageType>(root["type"].asInt());
        
        switch (type) {
            case MessageType::LOGIN:
                HandleLogin(client, root);
                break;
            case MessageType::REGISTER:
                HandleRegister(client, root);
                break;
            case MessageType::CHAT_MESSAGE:
                if (client->isAuthenticated) {
                    HandleChatMessage(client, root);
                } else {
                    SendErrorMessage(client->socket, "Not authenticated");
                }
                break;
            case MessageType::HISTORY_REQUEST:
                if (client->isAuthenticated) {
                    HandleHistoryRequest(client, root);
                } else {
                    SendErrorMessage(client->socket, "Not authenticated");
                }
                break;
            case MessageType::HEARTBEAT:
                // 心跳响应
                {
                    SimpleJson response;
                    response["type"] = static_cast<int>(MessageType::HEARTBEAT);
                    response["timestamp"] = GetCurrentTimestamp();
                    SendToClient(client->socket, response);
                }
                break;
            default:
                SendErrorMessage(client->socket, "Unknown message type");
                break;
        }
    } catch (const exception& e) {
        SendErrorMessage(client->socket, "Message processing error: " + string(e.what()));
    }
}

string ChatServer::GetCurrentTimestamp() {
    auto now = chrono::system_clock::now();
    auto time_t = chrono::system_clock::to_time_t(now);
    auto ms = chrono::duration_cast<chrono::milliseconds>(now.time_since_epoch()) % 1000;
    
    stringstream ss;
    ss << put_time(localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << setfill('0') << setw(3) << ms.count();
    return ss.str();
}

void ChatServer::Stop() {
    if (!isRunning) {
        return;
    }
    
    isRunning = false;
    
    // 关闭所有客户端连接
    {
        lock_guard<mutex> lock(clientsMutex);
        for (auto& pair : clients) {
            closesocket(pair.first);
        }
        clients.clear();
    }
    
    // 关闭服务器socket
    if (serverSocket != INVALID_SOCKET) {
        closesocket(serverSocket);
        serverSocket = INVALID_SOCKET;
    }
    
    WSACleanup();
    cout << "Chat server stopped" << endl;
}

int ChatServer::GetClientCount() const {
    lock_guard<mutex> lock(clientsMutex);
    return clients.size();
}

vector<string> ChatServer::GetConnectedUsers() const {
    vector<string> users;
    lock_guard<mutex> lock(clientsMutex);

    for (const auto& pair : clients) {
        if (pair.second->isAuthenticated) {
            users.push_back(pair.second->username);
        }
    }

    return users;
}

void ChatServer::HandleLogin(shared_ptr<ClientInfo> client, const SimpleJson& message) {
    if (!message.isMember("username") || !message.isMember("password")) {
        SendErrorMessage(client->socket, "Missing username or password");
        return;
    }

    string username = message["username"].asString();
    string password = message["password"].asString();
    string userId;

    if (AuthenticateUser(username, password, userId)) {
        client->username = username;
        client->userId = userId;
        client->isAuthenticated = true;

        // 发送登录成功响应
        SimpleJson response;
        response["type"] = static_cast<int>(MessageType::LOGIN);
        response["success"] = true;
        response["username"] = username;
        response["userId"] = userId;
        response["timestamp"] = GetCurrentTimestamp();
        SendToClient(client->socket, response);

        // 通知其他用户有新用户加入
        NotifyUserJoin(username);

        // 发送用户列表
        SendUserList(client->socket);

        cout << "User logged in: " << username << endl;
    } else {
        SimpleJson response;
        response["type"] = static_cast<int>(MessageType::LOGIN);
        response["success"] = false;
        response["error"] = "Invalid username or password";
        SendToClient(client->socket, response);
    }
}

void ChatServer::HandleRegister(shared_ptr<ClientInfo> client, const SimpleJson& message) {
    if (!message.isMember("username") || !message.isMember("password") || !message.isMember("email")) {
        SendErrorMessage(client->socket, "Missing required fields");
        return;
    }

    string username = message["username"].asString();
    string password = message["password"].asString();
    string email = message["email"].asString();

    if (RegisterUser(username, password, email)) {
        SimpleJson response;
        response["type"] = static_cast<int>(MessageType::REGISTER);
        response["success"] = true;
        response["message"] = "Registration successful";
        SendToClient(client->socket, response);

        cout << "New user registered: " << username << endl;
    } else {
        SimpleJson response;
        response["type"] = static_cast<int>(MessageType::REGISTER);
        response["success"] = false;
        response["error"] = "Registration failed - username may already exist";
        SendToClient(client->socket, response);
    }
}

void ChatServer::HandleChatMessage(shared_ptr<ClientInfo> client, const SimpleJson& message) {
    if (!message.isMember("content")) {
        SendErrorMessage(client->socket, "Missing message content");
        return;
    }

    string content = message["content"].asString();
    if (content.empty()) {
        return;
    }

    // 创建消息对象
    Message chatMessage(MessageType::CHAT_MESSAGE, client->userId, client->username, content);
    chatMessage.timestamp = GetCurrentTimestamp();

    // 保存到数据库
    SaveMessageToDatabase(chatMessage);

    // 添加到历史记录
    {
        lock_guard<mutex> lock(historyMutex);
        messageHistory.push_back(chatMessage);

        // 限制历史记录数量
        if (messageHistory.size() > 1000) {
            messageHistory.erase(messageHistory.begin());
        }
    }

    // 广播消息给所有客户端
    BroadcastMessage(chatMessage);

    cout << "[" << chatMessage.timestamp << "] " << client->username << ": " << content << endl;
}

void ChatServer::HandleHistoryRequest(shared_ptr<ClientInfo> client, const SimpleJson& message) {
    int limit = 50; // 默认返回最近50条消息
    if (message.isMember("limit")) {
        limit = message["limit"].asInt();
        limit = max(1, min(limit, 200)); // 限制在1-200之间
    }

    vector<Message> history;
    {
        lock_guard<mutex> lock(historyMutex);
        int start = max(0, static_cast<int>(messageHistory.size()) - limit);
        history.assign(messageHistory.begin() + start, messageHistory.end());
    }

    SimpleJson response;
    response["type"] = static_cast<int>(MessageType::HISTORY_RESPONSE);
    // 创建消息数组
    for (size_t i = 0; i < history.size(); ++i) {
        response["messages"][static_cast<int>(i)] = MessageToJson(history[i]);
    }



    SendToClient(client->socket, response);
}

void ChatServer::BroadcastMessage(const Message& message, SOCKET excludeSocket) {
    SimpleJson jsonMessage = MessageToJson(message);

    lock_guard<mutex> lock(clientsMutex);
    for (const auto& pair : clients) {
        if (pair.first != excludeSocket && pair.second->isAuthenticated) {
            SendToClient(pair.first, jsonMessage);
        }
    }
}

void ChatServer::SendToClient(SOCKET clientSocket, const SimpleJson& message) {
    string jsonString = message.toString() + "\n";

    int result = send(clientSocket, jsonString.c_str(), jsonString.length(), 0);
    if (result == SOCKET_ERROR) {
        cerr << "Send failed: " << WSAGetLastError() << endl;
    }
}

void ChatServer::SendUserList(SOCKET clientSocket) {
    SimpleJson response;
    response["type"] = static_cast<int>(MessageType::USER_LIST);

    {
        lock_guard<mutex> lock(clientsMutex);
        int userIndex = 0;
        for (const auto& pair : clients) {
            if (pair.second->isAuthenticated) {
                SimpleJson user;
                user["username"] = pair.second->username;
                user["userId"] = pair.second->userId;
                response["users"][userIndex] = user;
                userIndex++;
            }
        }
    }

    SendToClient(clientSocket, response);
}

void ChatServer::SendErrorMessage(SOCKET clientSocket, const string& error) {
    SimpleJson response;
    response["type"] = static_cast<int>(MessageType::ERROR_MESSAGE);
    response["error"] = error;
    response["timestamp"] = GetCurrentTimestamp();
    SendToClient(clientSocket, response);
}

bool ChatServer::AuthenticateUser(const string& username, const string& password, string& userId) {
    try {
        string sql = "SELECT user_id, password FROM info_user WHERE user_name = '" + username + "'";
        shared_ptr<ResultSet> rs = dbConnection->GetRecords(sql);

        if (rs && rs->next()) {
            string storedPassword = rs->getString("password");
            if (storedPassword == password) { // 在实际应用中应该使用哈希密码
                userId = rs->getString("user_id");
                return true;
            }
        }
    } catch (const exception& e) {
        cerr << "Authentication error: " << e.what() << endl;
    }

    return false;
}

bool ChatServer::RegisterUser(const string& username, const string& password, const string& email) {
    try {
        // 检查用户名是否已存在
        string checkSql = "SELECT COUNT(*) as count FROM info_user WHERE user_name = '" + username + "'";
        shared_ptr<ResultSet> rs = dbConnection->GetRecords(checkSql);

        if (rs && rs->next() && rs->getInt("count") > 0) {
            return false; // 用户名已存在
        }

        // 插入新用户
        string insertSql = "INSERT INTO info_user (user_name, password, email, created_at) VALUES ('"
                          + username + "', '" + password + "', '" + email + "', NOW())";

        return dbConnection->ExecuteUpdate(insertSql) > 0;
    } catch (const exception& e) {
        cerr << "Registration error: " << e.what() << endl;
        return false;
    }
}

void ChatServer::RemoveClient(SOCKET clientSocket) {
    lock_guard<mutex> lock(clientsMutex);
    auto it = clients.find(clientSocket);
    if (it != clients.end()) {
        closesocket(clientSocket);
        clients.erase(it);
    }
}

void ChatServer::NotifyUserJoin(const string& username) {
    Message joinMessage(MessageType::USER_JOIN, "", "System", username + " joined the chat");
    joinMessage.timestamp = GetCurrentTimestamp();
    BroadcastMessage(joinMessage);
}

void ChatServer::NotifyUserLeave(const string& username) {
    Message leaveMessage(MessageType::USER_LEAVE, "", "System", username + " left the chat");
    leaveMessage.timestamp = GetCurrentTimestamp();
    BroadcastMessage(leaveMessage);
}

void ChatServer::SaveMessageToDatabase(const Message& message) {
    try {
        string sql = "INSERT INTO chat_messages (sender_id, sender_name, content, message_type, timestamp) VALUES ('"
                    + message.senderId + "', '" + message.senderName + "', '" + message.content + "', "
                    + to_string(static_cast<int>(message.type)) + ", '" + message.timestamp + "')";

        dbConnection->ExecuteUpdate(sql);
    } catch (const exception& e) {
        cerr << "Error saving message to database: " << e.what() << endl;
    }
}

vector<Message> ChatServer::LoadMessageHistory(int limit) {
    vector<Message> history;

    try {
        string sql = "SELECT sender_id, sender_name, content, message_type, timestamp FROM chat_messages "
                    "WHERE message_type = " + to_string(static_cast<int>(MessageType::CHAT_MESSAGE)) +
                    " ORDER BY timestamp DESC LIMIT " + to_string(limit);

        shared_ptr<ResultSet> rs = dbConnection->GetRecords(sql);

        while (rs && rs->next()) {
            Message msg;
            msg.senderId = rs->getString("sender_id");
            msg.senderName = rs->getString("sender_name");
            msg.content = rs->getString("content");
            msg.type = static_cast<MessageType>(rs->getInt("message_type"));
            msg.timestamp = rs->getString("timestamp");

            history.push_back(msg);
        }

        // 反转顺序，使最新的消息在最后
        reverse(history.begin(), history.end());

    } catch (const exception& e) {
        cerr << "Error loading message history: " << e.what() << endl;
    }

    return history;
}

SimpleJson ChatServer::MessageToJson(const Message& message) {
    SimpleJson json;
    json["type"] = static_cast<int>(message.type);
    json["senderId"] = message.senderId;
    json["senderName"] = message.senderName;
    json["content"] = message.content;
    json["timestamp"] = message.timestamp;
    return json;
}

Message ChatServer::JsonToMessage(const SimpleJson& json) {
    Message message;
    message.type = static_cast<MessageType>(json["type"].asInt());
    message.senderId = json["senderId"].asString();
    message.senderName = json["senderName"].asString();
    message.content = json["content"].asString();
    message.timestamp = json["timestamp"].asString();
    return message;
}
