/*
 * Copyright (c) 2014, 2024, Oracle and/or its affiliates.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License, version 2.0, as
 * published by the Free Software Foundation.
 *
 * This program is designed to work with certain software (including
 * but not limited to OpenSSL) that is licensed under separate terms, as
 * designated in a particular file or component or in included license
 * documentation. The authors of MySQL hereby grant you an additional
 * permission to link the program and your derivative works with the
 * separately licensed software that they have either included with
 * the program or referenced in the documentation.
 *
 * Without limiting anything contained in the foregoing, this file,
 * which is part of Connector/C++, is also subject to the
 * Universal FOSS Exception, version 1.0, a copy of which can be found at
 * https://oss.oracle.com/licenses/universal-foss-exception.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 * See the GNU General Public License, version 2.0, for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA
 */

/*  */

#define MYCPPCONN_DM_MAJOR_VERSION 9
#define MYCPPCONN_DM_MINOR_VERSION 4
#define MYCPPCONN_DM_PATCH_VERSION 0

#define MYCPPCONN_DM_VERSION     "9.4.0"
#define MYCPPCONN_DM_VERSION_ID  9040000
#define MYSQL_CONCPP_LICENSE     "GPL-2.0"

#define MYSQL_CONCPP_VERSION_MAJOR   9
#define MYSQL_CONCPP_VERSION_MINOR   4
#define MYSQL_CONCPP_VERSION_MICRO   0

#define MYSQL_CONCPP_VERSION_NUMBER  9040000


/* Driver version info */

#define MYCPPCONN_STATIC_MYSQL_VERSION     "9.4.0"
#define MYCPPCONN_STATIC_MYSQL_VERSION_ID  90400

#define MYCPPCONN_BOOST_VERSION            
