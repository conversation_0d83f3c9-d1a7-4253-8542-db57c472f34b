@echo off
echo Building SimpleJson test...

REM Try to find Visual Studio installation
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if exist "%VSWHERE%" (
    for /f "usebackq tokens=*" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
        set "VS_PATH=%%i"
    )
)

if defined VS_PATH (
    echo Found Visual Studio at: %VS_PATH%
    call "%VS_PATH%\VC\Auxiliary\Build\vcvars64.bat"
    
    echo Compiling with MSVC...
    cl /EHsc /std:c++17 /I"include" src\main.cpp /Fe:simple_test_vs.exe
    
    if %ERRORLEVEL% EQU 0 (
        echo Build successful! Running test...
        simple_test_vs.exe
    ) else (
        echo Build failed with MSVC
    )
) else (
    echo Visual Studio not found, trying with g++...
    g++ -std=c++17 -I"include" src\main.cpp -o simple_test_gcc.exe
    
    if %ERRORLEVEL% EQU 0 (
        echo Build successful with g++! Running test...
        simple_test_gcc.exe
    ) else (
        echo Build failed with g++ as well
    )
)

pause
