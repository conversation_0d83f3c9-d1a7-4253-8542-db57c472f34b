#include <iostream>
#include "include/SimpleJson.h"

using namespace std;

int main() {
    cout << "=== Complete SimpleJson Test ===" << endl;
    
    // Test 1: Basic JSON creation and serialization
    cout << "\n1. Testing basic JSON creation:" << endl;
    SimpleJson json1;
    json1["type"] = 1;
    json1["username"] = "testuser";
    json1["message"] = "Hello World";
    json1["success"] = true;
    cout << "Created: " << json1.toString() << endl;
    
    // Test 2: Array operations
    cout << "\n2. Testing array operations:" << endl;
    SimpleJson jsonArray;
    jsonArray[0] = "first";
    jsonArray[1] = "second";
    jsonArray[2] = 42;
    cout << "Array: " << jsonArray.toString() << endl;
    
    // Test 3: Nested objects
    cout << "\n3. Testing nested objects:" << endl;
    SimpleJson nested;
    nested["user"]["name"] = "John";
    nested["user"]["age"] = 25;
    nested["user"]["active"] = true;
    nested["messages"][0] = "Hello";
    nested["messages"][1] = "World";
    cout << "Nested: " << nested.toString() << endl;
    
    // Test 4: JSON parsing
    cout << "\n4. Testing JSON parsing:" << endl;
    string jsonStr = R"({"name":"Alice","age":30,"hobbies":["reading","coding"]})";
    SimpleJson parsed = SimpleJson::parse(jsonStr);
    cout << "Original: " << jsonStr << endl;
    cout << "Parsed: " << parsed.toString() << endl;
    cout << "Name: " << parsed["name"].asString() << endl;
    cout << "Age: " << parsed["age"].asInt() << endl;
    
    // Test 5: jsoncpp compatibility
    cout << "\n5. Testing jsoncpp compatibility:" << endl;
    Json::Value jsonCompat;
    jsonCompat["status"] = "ok";
    jsonCompat["code"] = 200;
    cout << "Compatible: " << jsonCompat.toString() << endl;
    
    // Test 6: Type checking
    cout << "\n6. Testing type checking:" << endl;
    SimpleJson typeTest;
    typeTest["string"] = "hello";
    typeTest["number"] = 123;
    typeTest["boolean"] = false;
    
    cout << "string is string: " << typeTest["string"].isString() << endl;
    cout << "number is int: " << typeTest["number"].isInt() << endl;
    cout << "boolean is bool: " << typeTest["boolean"].isBool() << endl;
    
    cout << "\n=== All tests completed successfully! ===" << endl;
    return 0;
}
