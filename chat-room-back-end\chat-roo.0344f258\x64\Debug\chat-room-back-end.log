﻿  ChatServer.cpp
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\src\ChatServer.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\ChatServer.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(94,9): error C2059: 语法错误:“switch”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(94,23): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(126,9): error C2059: 语法错误:“return”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(126,24): error C2238: 意外的标记位于“;”之前
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(130,9): error C2377: “size_t”: 重定义；typedef 不能由任何其他符号重载
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\src\ChatServer.cpp(1,1):
      参见“size_t”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(130,16): error C2146: 语法错误: 缺少“;”(在标识符“pos”的前面)
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(130,20): error C2059: 语法错误:“=”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(131,9): error C2059: 语法错误:“return”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(132,5): error C2059: 语法错误:“}”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(132,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(150,51): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(151,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(151,50): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(152,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(156,53): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(157,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(159,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(161,22): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(163,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(163,20): error C3861: “parseString”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(165,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(165,20): error C3861: “parseObject”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(167,36): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(167,20): error C3861: “parseArray”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(169,35): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(169,20): error C3861: “parseBool”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(171,35): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(171,20): error C3861: “parseNull”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(173,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(173,20): error C3861: “parseNumber”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(179,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(180,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(180,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(182,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(185,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(185,42): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(186,21): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(186,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(187,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(188,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(194,44): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(197,31): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(199,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(202,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(202,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(206,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(207,16): error C2146: 语法错误: 缺少“;”(在标识符“start”的前面)
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(207,16): error C2065: “start”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(207,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(208,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(208,30): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(210,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(210,50): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(211,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(214,36): error C2065: “start”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(214,43): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(214,49): error C2065: “start”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(218,52): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(219,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(220,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(222,31): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(223,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(229,52): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(230,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(231,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(236,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(238,12): error C2248: “SimpleJson::type”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(23,15):
      参见“SimpleJson::type”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(238,20): error C2065: “OBJECT_VALUE”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(241,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(243,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(244,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(244,39): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(245,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(249,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(250,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(253,47): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(256,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(257,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(257,44): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(258,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(261,48): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(262,16): error C2248: “SimpleJson::objectValue”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(28,29):
      参见“SimpleJson::objectValue”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(262,45): error C2280: “SimpleJson &SimpleJson::operator =(const SimpleJson &)”: 尝试引用已删除的函数
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(127,5):
      编译器已在此处生成“SimpleJson::operator =”
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(127,5):
      “SimpleJson &SimpleJson::operator =(const SimpleJson &)”: 由于 数据成员 调用已删除或不可访问的函数“std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>>::operator =(const std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &)”，因此已隐式删除函数
      D:\TOOLS\learning\Visual Stdio2022\IDE\VC\Tools\MSVC\14.44.35207\include\sstream(918,25):
      “std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>>::operator =(const std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &)”: 已隐式删除函数
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(264,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(265,17): error C2065: “pos”: 未声明的标识符
  main.cpp
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(267,21): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(268,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(270,28): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(271,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(280,53): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(282,12): error C2248: “SimpleJson::type”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(23,15):
      参见“SimpleJson::type”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(282,20): error C2065: “ARRAY_VALUE”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(284,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(284,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(285,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(287,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,39): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,39): error C1003: 错误计数超过 100；正在停止编译
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\src\main.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\ChatServer.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(94,9): error C2059: 语法错误:“switch”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(94,23): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(126,9): error C2059: 语法错误:“return”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(126,24): error C2238: 意外的标记位于“;”之前
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(130,9): error C2377: “size_t”: 重定义；typedef 不能由任何其他符号重载
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\src\main.cpp(1,1):
      参见“size_t”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(130,16): error C2146: 语法错误: 缺少“;”(在标识符“pos”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(130,20): error C2059: 语法错误:“=”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(131,9): error C2059: 语法错误:“return”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(132,5): error C2059: 语法错误:“}”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(132,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(150,51): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(151,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(151,50): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(152,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(156,53): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(157,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(159,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(161,22): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(163,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(163,20): error C3861: “parseString”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(165,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(165,20): error C3861: “parseObject”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(167,36): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(167,20): error C3861: “parseArray”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(169,35): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(169,20): error C3861: “parseBool”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(171,35): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(171,20): error C3861: “parseNull”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(173,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(173,20): error C3861: “parseNumber”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(179,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(180,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(180,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(182,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(185,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(185,42): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(186,21): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(186,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(187,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(188,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(194,44): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(197,31): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(199,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(202,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(202,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(206,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(207,16): error C2146: 语法错误: 缺少“;”(在标识符“start”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(207,16): error C2065: “start”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(207,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(208,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(208,30): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(210,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(210,50): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(211,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(214,36): error C2065: “start”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(214,43): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(214,49): error C2065: “start”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(218,52): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(219,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(220,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(222,31): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(223,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(229,52): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(230,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(231,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(236,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(238,12): error C2248: “SimpleJson::type”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(23,15):
      参见“SimpleJson::type”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(238,20): error C2065: “OBJECT_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(241,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(243,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(244,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(244,39): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(245,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(249,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(250,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(253,47): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(256,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(257,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(257,44): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(258,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(261,48): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(262,16): error C2248: “SimpleJson::objectValue”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(28,29):
      参见“SimpleJson::objectValue”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(262,45): error C2280: “SimpleJson &SimpleJson::operator =(const SimpleJson &)”: 尝试引用已删除的函数
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(127,5):
      编译器已在此处生成“SimpleJson::operator =”
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(127,5):
      “SimpleJson &SimpleJson::operator =(const SimpleJson &)”: 由于 数据成员 调用已删除或不可访问的函数“std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>>::operator =(const std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &)”，因此已隐式删除函数
      D:\TOOLS\learning\Visual Stdio2022\IDE\VC\Tools\MSVC\14.44.35207\include\sstream(918,25):
      “std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>>::operator =(const std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &)”: 已隐式删除函数
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(264,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(265,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(267,21): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(268,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(270,28): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(271,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(280,53): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(282,12): error C2248: “SimpleJson::type”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(23,15):
      参见“SimpleJson::type”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(282,20): error C2065: “ARRAY_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(284,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(284,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(285,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(287,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,39): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,39): error C1003: 错误计数超过 100；正在停止编译
  (编译源文件“src/main.cpp”)
  
  User.cpp
  正在生成代码...
\src\User.cpp(1,1): error C1083: 无法打开源文件: “src\User.cpp”: No such file or directory
  (编译源文件“/src/User.cpp”)
  
