﻿  DbConnect.cpp
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\DbConnect.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/DbConnect.cpp”)
  
  main.cpp
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(47,9): error C2059: 语法错误:“if”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(47,47): error C2238: 意外的标记位于“;”之前
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(48,9): error C2059: 语法错误:“if”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(48,35): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(52,9): error C2059: 语法错误:“return”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(52,17): error C2238: 意外的标记位于“;”之前
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(55,5): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(13,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(55,12): error C2146: 语法错误: 缺少“;”(在标识符“asString”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(55,21): error C2059: 语法错误:“)”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(55,29): error C2143: 语法错误: 缺少“;”(在“{”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(55,29): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(62,10): error C2270: “asBool”: 非成员函数上不允许修饰符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(63,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(63,21): error C2065: “BOOL_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(63,40): error C2065: “boolValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(64,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(64,21): error C2065: “INT_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(64,39): error C2065: “intValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(65,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(65,21): error C2065: “STRING_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(65,43): error C2065: “stringValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(70,34): error C2872: “string”: 不明确的符号
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(55,5):
      可能是“SimpleJson string”
      D:\TOOLS\learning\Visual Stdio2022\IDE\VC\Tools\MSVC\14.44.35207\include\xstring(3378,19):
      或    “std::string”
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(70,28): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(70,40): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(70,17): error C3315: “operator []”: 必须是成员函数
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(71,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(71,21): error C2065: “OBJECT_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(72,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(72,20): error C2065: “OBJECT_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(73,13): error C2065: “objectValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(75,16): error C2065: “objectValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(75,28): error C2065: “key”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(78,40): error C2872: “string”: 不明确的符号
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(55,5):
      可能是“SimpleJson string”
      D:\TOOLS\learning\Visual Stdio2022\IDE\VC\Tools\MSVC\14.44.35207\include\xstring(3378,19):
      或    “std::string”
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(78,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(78,46): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(78,23): error C2270: “[]”: 非成员函数上不允许修饰符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(78,23): error C2556: “const SimpleJson &operator [](const int)”: 重载函数与“SimpleJson &operator [](const int)”只是在返回类型上不同
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(70,17):
      参见“operator []”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(78,23): error C3315: “operator []”: 必须是成员函数
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(79,27): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(13,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(80,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(80,21): error C2065: “OBJECT_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(81,23): error C2065: “objectValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(81,40): error C2065: “key”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(82,17): error C3536: “it”: 初始化之前无法使用
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(82,23): error C2065: “objectValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(90,17): error C3315: “operator []”: 必须是成员函数
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(91,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(91,21): error C2065: “ARRAY_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(92,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(92,20): error C2065: “ARRAY_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(93,13): error C2065: “arrayValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(95,39): error C2065: “arrayValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(96,13): error C2065: “arrayValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(98,16): error C2065: “arrayValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(102,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(102,21): error C2065: “ARRAY_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(103,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(103,20): error C2065: “ARRAY_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(104,13): error C2065: “arrayValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(106,9): error C2065: “arrayValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(109,12): error C2270: “size”: 非成员函数上不允许修饰符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(110,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(110,21): error C2065: “ARRAY_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(110,41): error C2065: “arrayValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(111,13): error C2065: “type”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(111,21): error C2065: “OBJECT_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(111,42): error C2065: “objectValue”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(117,9): error C2059: 语法错误:“switch”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(117,23): error C2143: 语法错误: 缺少“;”(在“{”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(117,23): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(151,9): error C2059: 语法错误:“return”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(152,5): error C2059: 语法错误:“}”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(152,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(156,9): error C2059: 语法错误:“return”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(157,5): error C2059: 语法错误:“}”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(157,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(160,12): error C2872: “string”: 不明确的符号
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(55,5):
      可能是“SimpleJson string”
      D:\TOOLS\learning\Visual Stdio2022\IDE\VC\Tools\MSVC\14.44.35207\include\xstring(3378,19):
      或    “std::string”
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(160,12): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(160,12): error C2371: “string”: 重定义；不同的基类型
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(55,5):
      参见“string”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(160,19): error C2146: 语法错误: 缺少“;”(在标识符“escapeString”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(160,32): error C2059: 语法错误:“const”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(160,51): error C2143: 语法错误: 缺少“;”(在“{”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(160,51): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
  (编译源文件“src/main.cpp”)
  
  正在生成代码...
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(183,51): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(183,51):
      “SimpleJson::SimpleJson”: 没有合适的默认构造函数可用
          D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(183,51):
          尝试匹配参数列表“()”时
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(187,20): error C3861: “parseString”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(189,20): error C3861: “parseObject”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(191,20): error C3861: “parseArray”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(193,20): error C3861: “parseBool”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(195,20): error C3861: “parseNull”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(197,20): error C3861: “parseNumber”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(200,26): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(200,26):
      “SimpleJson::SimpleJson”: 没有合适的默认构造函数可用
          D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(200,26):
          尝试匹配参数列表“()”时
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(204,70): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(204,70):
      “SimpleJson::SimpleJson”: 没有合适的默认构造函数可用
          D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(204,70):
          尝试匹配参数列表“()”时
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(242,34): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(242,34):
      “SimpleJson::SimpleJson”: 没有合适的默认构造函数可用
          D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(242,34):
          尝试匹配参数列表“()”时
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(245,26): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(245,26):
      “SimpleJson::SimpleJson”: 没有合适的默认构造函数可用
          D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(245,26):
          尝试匹配参数列表“()”时
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(256,26): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(256,26):
      “SimpleJson::SimpleJson”: 没有合适的默认构造函数可用
          D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(256,26):
          尝试匹配参数列表“()”时
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(263,26): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(263,26):
      “SimpleJson::SimpleJson”: 没有合适的默认构造函数可用
          D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(263,26):
          尝试匹配参数列表“()”时
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(267,20): error C2512: “SimpleJson”: 没有合适的默认构造函数可用
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(13,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(268,12): error C2248: “SimpleJson::type”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(25,15):
      参见“SimpleJson::type”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(13,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(268,20): error C2065: “OBJECT_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(292,16): error C2248: “SimpleJson::objectValue”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(30,29):
      参见“SimpleJson::objectValue”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(13,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(292,33): error C2039: "asString": 不是 "SimpleJson" 的成员
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(13,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(292,43): error C7732: “]”前面应该有一个表达式
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(292,43): error C1003: 错误计数超过 100；正在停止编译
  (编译源文件“src/main.cpp”)
  
