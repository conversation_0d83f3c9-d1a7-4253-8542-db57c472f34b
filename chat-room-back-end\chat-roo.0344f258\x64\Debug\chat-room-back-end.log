﻿  ChatServer.cpp
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\src\ChatServer.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\ChatServer.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(120,9): error C2059: 语法错误:“switch”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(120,23): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(152,9): error C2059: 语法错误:“return”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(152,24): error C2238: 意外的标记位于“;”之前
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(156,9): error C2377: “size_t”: 重定义；typedef 不能由任何其他符号重载
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\src\ChatServer.cpp(1,1):
      参见“size_t”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(156,16): error C2146: 语法错误: 缺少“;”(在标识符“pos”的前面)
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(156,20): error C2059: 语法错误:“=”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(157,9): error C2059: 语法错误:“return”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(158,5): error C2059: 语法错误:“}”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(158,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(176,51): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(177,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(177,50): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(178,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(182,53): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(183,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(185,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(187,22): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(189,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(189,20): error C3861: “parseString”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(191,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(191,20): error C3861: “parseObject”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(193,36): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(193,20): error C3861: “parseArray”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(195,35): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(195,20): error C3861: “parseBool”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(197,35): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(197,20): error C3861: “parseNull”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(199,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(199,20): error C3861: “parseNumber”: 找不到标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(205,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(206,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(206,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(208,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(211,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(211,42): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(212,21): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(212,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(213,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(214,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(220,44): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(223,31): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(225,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(228,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(228,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(232,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(233,16): error C2146: 语法错误: 缺少“;”(在标识符“start”的前面)
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(233,16): error C2065: “start”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(233,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(234,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(234,30): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(236,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(236,50): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(237,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,36): error C2065: “start”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,43): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,49): error C2065: “start”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(244,52): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(245,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(246,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(248,31): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(249,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(255,52): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(256,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(257,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(262,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(264,12): error C2248: “SimpleJson::type”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(23,15):
      参见“SimpleJson::type”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(264,20): error C2065: “OBJECT_VALUE”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(266,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(266,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(267,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(269,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(270,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(270,39): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(271,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(275,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(276,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(279,47): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(282,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(283,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(283,44): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(284,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(287,48): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,16): error C2248: “SimpleJson::objectValue”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(28,29):
      参见“SimpleJson::objectValue”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,45): error C2280: “SimpleJson &SimpleJson::operator =(const SimpleJson &)”: 尝试引用已删除的函数
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(153,5):
      编译器已在此处生成“SimpleJson::operator =”
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(153,5):
      “SimpleJson &SimpleJson::operator =(const SimpleJson &)”: 由于 数据成员 调用已删除或不可访问的函数“std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>>::operator =(const std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &)”，因此已隐式删除函数
      D:\TOOLS\learning\Visual Stdio2022\IDE\VC\Tools\MSVC\14.44.35207\include\sstream(918,25):
      “std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>>::operator =(const std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &)”: 已隐式删除函数
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(290,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(291,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(293,21): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(294,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(296,28): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(297,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(306,53): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(308,12): error C2248: “SimpleJson::type”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/ChatServer.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(23,15):
      参见“SimpleJson::type”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(308,20): error C2065: “ARRAY_VALUE”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(310,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(310,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(311,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(313,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(314,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(314,39): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/ChatServer.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(314,39): error C1003: 错误计数超过 100；正在停止编译
  (编译源文件“src/ChatServer.cpp”)
  
  main.cpp
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(120,9): error C2059: 语法错误:“switch”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(120,23): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(152,9): error C2059: 语法错误:“return”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(152,24): error C2238: 意外的标记位于“;”之前
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(156,9): error C2377: “size_t”: 重定义；typedef 不能由任何其他符号重载
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\src\main.cpp(1,1):
      参见“size_t”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(156,16): error C2146: 语法错误: 缺少“;”(在标识符“pos”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(156,20): error C2059: 语法错误:“=”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(157,9): error C2059: 语法错误:“return”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(158,5): error C2059: 语法错误:“}”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(158,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(176,51): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(177,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(177,50): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(178,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(182,53): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(183,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(185,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(187,22): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(189,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(189,20): error C3861: “parseString”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(191,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(191,20): error C3861: “parseObject”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(193,36): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(193,20): error C3861: “parseArray”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(195,35): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(195,20): error C3861: “parseBool”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(197,35): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(197,20): error C3861: “parseNull”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(199,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(199,20): error C3861: “parseNumber”: 找不到标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(205,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(206,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(206,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(208,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(211,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(211,42): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(212,21): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(212,37): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(213,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(214,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(220,44): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(223,31): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(225,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(228,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(228,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(232,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(233,16): error C2146: 语法错误: 缺少“;”(在标识符“start”的前面)
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(233,16): error C2065: “start”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(233,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(234,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(234,30): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(236,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(236,50): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(237,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,36): error C2065: “start”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,43): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(240,49): error C2065: “start”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(244,52): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(245,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(246,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(248,31): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(249,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(255,52): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(256,24): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(257,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(262,54): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(264,12): error C2248: “SimpleJson::type”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(23,15):
      参见“SimpleJson::type”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(264,20): error C2065: “OBJECT_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(266,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(266,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(267,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(269,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(270,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(270,39): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(271,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(275,16): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(276,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(279,47): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(282,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(283,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(283,44): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(284,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(287,48): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,16): error C2248: “SimpleJson::objectValue”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(28,29):
      参见“SimpleJson::objectValue”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(288,45): error C2280: “SimpleJson &SimpleJson::operator =(const SimpleJson &)”: 尝试引用已删除的函数
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(153,5):
      编译器已在此处生成“SimpleJson::operator =”
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(153,5):
      “SimpleJson &SimpleJson::operator =(const SimpleJson &)”: 由于 数据成员 调用已删除或不可访问的函数“std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>>::operator =(const std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &)”，因此已隐式删除函数
      D:\TOOLS\learning\Visual Stdio2022\IDE\VC\Tools\MSVC\14.44.35207\include\sstream(918,25):
      “std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>>::operator =(const std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>> &)”: 已隐式删除函数
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(290,33): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(291,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(293,21): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(294,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(296,28): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(297,17): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(306,53): error C2061: 语法错误: 标识符“size_t”
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(308,12): error C2248: “SimpleJson::type”: 无法访问 private 成员(在“SimpleJson”类中声明)
  (编译源文件“src/main.cpp”)
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(23,15):
      参见“SimpleJson::type”的声明
      D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(11,7):
      参见“SimpleJson”的声明
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(308,20): error C2065: “ARRAY_VALUE”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(310,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(310,40): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(311,9): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(313,29): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(314,13): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(314,39): error C2065: “pos”: 未声明的标识符
  (编译源文件“src/main.cpp”)
  
D:\TOOLS\learning\JavaScriptProject\ChatRoom\chat-room-back-end\include\SimpleJson.h(314,39): error C1003: 错误计数超过 100；正在停止编译
  (编译源文件“src/main.cpp”)
  
  正在生成代码...
