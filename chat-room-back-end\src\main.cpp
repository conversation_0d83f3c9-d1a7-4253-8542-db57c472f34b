#include <iostream>
#include <string>
#include "../include/SimpleJson.h"

using namespace std;

int main() {
    cout << "=== TCP Chat Room Server ===" << endl;
    cout << "Testing SimpleJson implementation..." << endl;

    // 测试SimpleJson功能
    try {
        SimpleJson testJson;
        testJson["type"] = 1;
        testJson["username"] = "testuser";
        testJson["message"] = "Hello World";
        testJson["success"] = true;

        cout << "Created JSON: " << testJson.toString() << endl;

        // 测试解析
        string jsonStr = "{\"type\":1,\"username\":\"testuser\",\"message\":\"Hello World\"}";
        SimpleJson parsed = SimpleJson::parse(jsonStr);

        cout << "Parsed JSON type: " << parsed["type"].asInt() << endl;
        cout << "Parsed JSON username: " << parsed["username"].asString() << endl;
        cout << "Parsed JSON message: " << parsed["message"].asString() << endl;

        cout << "SimpleJson test completed successfully!" << endl;

    } catch (const exception& e) {
        cerr << "Error: " << e.what() << endl;
        return 1;
    }

    cout << "Press Enter to exit..." << endl;
    cin.get();

    return 0;
}
