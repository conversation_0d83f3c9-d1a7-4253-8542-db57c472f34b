#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include "../include/ChatServer.h"

using namespace std;

void printServerInfo(ChatServer& server) {
    while (server.IsRunning()) {
        this_thread::sleep_for(chrono::seconds(30));
        
        if (server.IsRunning()) {
            cout << "\n=== Server Status ===" << endl;
            cout << "Connected clients: " << server.GetClientCount() << endl;
            
            vector<string> users = server.GetConnectedUsers();
            if (!users.empty()) {
                cout << "Online users: ";
                for (size_t i = 0; i < users.size(); ++i) {
                    cout << users[i];
                    if (i < users.size() - 1) cout << ", ";
                }
                cout << endl;
            }
            cout << "===================" << endl;
        }
    }
}

int main() {
    cout << "=== TCP Chat Room Server ===" << endl;
    cout << "Starting server..." << endl;
    
    // 创建聊天服务器实例
    ChatServer server(8080);
    
    // 启动服务器
    if (!server.Start()) {
        cerr << "Failed to start server!" << endl;
        return 1;
    }
    
    // 启动状态监控线程
    thread statusThread(printServerInfo, ref(server));
    statusThread.detach();
    
    cout << "Server is running. Press 'q' and Enter to quit." << endl;
    
    // 主循环
    thread serverThread([&server]() {
        server.Run();
    });
    
    // 等待用户输入退出命令
    string input;
    while (true) {
        getline(cin, input);
        if (input == "q" || input == "quit" || input == "exit") {
            break;
        } else if (input == "status") {
            cout << "\n=== Current Status ===" << endl;
            cout << "Connected clients: " << server.GetClientCount() << endl;
            
            vector<string> users = server.GetConnectedUsers();
            if (!users.empty()) {
                cout << "Online users: ";
                for (size_t i = 0; i < users.size(); ++i) {
                    cout << users[i];
                    if (i < users.size() - 1) cout << ", ";
                }
                cout << endl;
            } else {
                cout << "No users online" << endl;
            }
            cout << "=====================" << endl;
        } else if (input == "help") {
            cout << "\nAvailable commands:" << endl;
            cout << "  status - Show server status" << endl;
            cout << "  help   - Show this help message" << endl;
            cout << "  q/quit/exit - Stop the server" << endl;
        }
    }
    
    cout << "Shutting down server..." << endl;
    server.Stop();
    
    if (serverThread.joinable()) {
        serverThread.join();
    }
    
    cout << "Server stopped. Goodbye!" << endl;
    return 0;
}
