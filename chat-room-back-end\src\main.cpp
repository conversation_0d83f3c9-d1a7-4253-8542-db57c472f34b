#include <iostream>
#include <string>
#include "../include/SimpleJson.h"

using namespace std;

// 暂时注释掉ChatServer相关代码
/*
void printServerInfo(ChatServer& server) {
    while (server.IsRunning()) {
        this_thread::sleep_for(chrono::seconds(30));

        if (server.IsRunning()) {
            cout << "\n=== Server Status ===" << endl;
            cout << "Connected clients: " << server.GetClientCount() << endl;

            vector<string> users = server.GetConnectedUsers();
            if (!users.empty()) {
                cout << "Online users: ";
                for (size_t i = 0; i < users.size(); ++i) {
                    cout << users[i];
                    if (i < users.size() - 1) cout << ", ";
                }
                cout << endl;
            }
            cout << "===================" << endl;
        }
    }
}
*/

int main() {
    cout << "=== TCP Chat Room Server ===" << endl;
    cout << "Testing SimpleJson implementation..." << endl;

    // 测试SimpleJson功能
    try {
        SimpleJson testJson;
        testJson["type"] = 1;
        testJson["username"] = "testuser";
        testJson["message"] = "Hello World";
        testJson["success"] = true;

        cout << "Created JSON: " << testJson.toString() << endl;

        // 测试解析
        string jsonStr = "{\"type\":1,\"username\":\"testuser\",\"message\":\"Hello World\"}";
        SimpleJson parsed = SimpleJson::parse(jsonStr);

        cout << "Parsed JSON type: " << parsed["type"].asInt() << endl;
        cout << "Parsed JSON username: " << parsed["username"].asString() << endl;
        cout << "Parsed JSON message: " << parsed["message"].asString() << endl;

        cout << "SimpleJson test completed successfully!" << endl;

    } catch (const exception& e) {
        cerr << "Error: " << e.what() << endl;
        return 1;
    }

    cout << "Press Enter to exit..." << endl;
    cin.get();

    return 0;
}
