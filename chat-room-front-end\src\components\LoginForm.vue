<template>
  <div class="login-container">
    <div class="login-form">
      <h2>{{ isRegisterMode ? 'Register' : 'Login' }}</h2>
      
      <form @submit.prevent="handleSubmit">
        <div class="form-group">
          <label for="username">Username:</label>
          <input
            id="username"
            v-model="formData.username"
            type="text"
            required
            :disabled="isLoading"
            placeholder="Enter your username"
          />
        </div>

        <div v-if="isRegisterMode" class="form-group">
          <label for="email">Email:</label>
          <input
            id="email"
            v-model="formData.email"
            type="email"
            required
            :disabled="isLoading"
            placeholder="Enter your email"
          />
        </div>

        <div class="form-group">
          <label for="password">Password:</label>
          <input
            id="password"
            v-model="formData.password"
            type="password"
            required
            :disabled="isLoading"
            placeholder="Enter your password"
          />
        </div>

        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>

        <div v-if="successMessage" class="success-message">
          {{ successMessage }}
        </div>

        <button 
          type="submit" 
          class="submit-btn"
          :disabled="isLoading"
        >
          <span v-if="isLoading">{{ isRegisterMode ? 'Registering...' : 'Logging in...' }}</span>
          <span v-else>{{ isRegisterMode ? 'Register' : 'Login' }}</span>
        </button>
      </form>

      <div class="form-footer">
        <p>
          {{ isRegisterMode ? 'Already have an account?' : "Don't have an account?" }}
          <button 
            @click="toggleMode" 
            class="toggle-btn"
            :disabled="isLoading"
          >
            {{ isRegisterMode ? 'Login' : 'Register' }}
          </button>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
// import { connectToServer, sendMessage } from '../api/websocket'

const emit = defineEmits(['login'])

const isRegisterMode = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

const formData = reactive({
  username: '',
  email: '',
  password: ''
})

const toggleMode = () => {
  isRegisterMode.value = !isRegisterMode.value
  errorMessage.value = ''
  successMessage.value = ''
  formData.email = ''
}

const handleSubmit = async () => {
  if (isLoading.value) return

  errorMessage.value = ''
  successMessage.value = ''
  isLoading.value = true

  try {
    // 临时模拟登录逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (isRegisterMode.value) {
      successMessage.value = 'Registration successful! Please login.'
      isRegisterMode.value = false
      formData.password = ''
      formData.email = ''
    } else {
      // 简单的登录验证
      if (formData.username && formData.password) {
        const userData = {
          username: formData.username,
          userId: '1'
        }
        emit('login', userData)
      } else {
        errorMessage.value = 'Please enter username and password'
      }
    }
  } catch (error) {
    console.error('Login error:', error)
    errorMessage.value = 'Login failed. Please try again.'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
  width: 100%;
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 400px;
}

.login-form h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #555;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #fcc;
  font-size: 0.9rem;
}

.success-message {
  background: #efe;
  color: #363;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #cfc;
  font-size: 0.9rem;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.875rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.form-footer {
  text-align: center;
  color: #666;
}

.toggle-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
  margin-left: 0.5rem;
}

.toggle-btn:hover:not(:disabled) {
  color: #764ba2;
}

.toggle-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
