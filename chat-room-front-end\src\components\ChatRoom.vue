<template>
  <div class="chat-room">
    <div class="chat-container">
      <!-- 用户列表侧边栏 -->
      <div class="users-sidebar">
        <div class="sidebar-header">
          <h3>Online Users ({{ onlineUsers.length }})</h3>
        </div>
        <div class="users-list">
          <div 
            v-for="user in onlineUsers" 
            :key="user.userId || user.username"
            class="user-item"
            :class="{ 'current-user': user.username === props.user.username }"
          >
            <div class="user-avatar">{{ user.username.charAt(0).toUpperCase() }}</div>
            <span class="user-name">{{ user.username }}</span>
            <span v-if="user.username === props.user.username" class="you-label">(You)</span>
          </div>
        </div>
      </div>

      <!-- 聊天主区域 -->
      <div class="chat-main">
        <!-- 消息显示区域 -->
        <div class="messages-container" ref="messagesContainer">
          <div class="messages-list">
            <div 
              v-for="message in messages" 
              :key="message.id || message.timestamp"
              class="message-item"
              :class="{ 
                'own-message': message.senderName === props.user.username,
                'system-message': message.senderName === 'System'
              }"
            >
              <div class="message-header">
                <span class="sender-name">{{ message.senderName }}</span>
                <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-content">{{ message.content }}</div>
            </div>
          </div>
        </div>

        <!-- 消息输入区域 -->
        <div class="message-input-container">
          <form @submit.prevent="sendMessage" class="message-form">
            <input
              v-model="newMessage"
              type="text"
              placeholder="Type your message..."
              class="message-input"
              :disabled="!isConnected"
              maxlength="500"
            />
            <button 
              type="submit" 
              class="send-button"
              :disabled="!newMessage.trim() || !isConnected"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
              </svg>
            </button>
          </form>
          <div class="connection-status" :class="{ 'connected': isConnected, 'disconnected': !isConnected }">
            {{ isConnected ? 'Connected' : 'Disconnected' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
// import { sendMessage, onMessage, disconnect } from '../api/websocket'

const props = defineProps({
  user: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['logout'])

const messages = ref([
  {
    id: 1,
    senderName: 'System',
    content: 'Welcome to the chat room!',
    timestamp: new Date().toISOString(),
    type: 'system'
  }
])
const onlineUsers = ref([
  { username: props.user.username, userId: props.user.userId }
])
const newMessage = ref('')
const isConnected = ref(true)
const messagesContainer = ref(null)

// 添加消息到列表
const addMessage = (message) => {
  messages.value.push({
    ...message,
    id: Date.now() + Math.random()
  })
  scrollToBottom()
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 发送消息
const sendMessage = async () => {
  if (!newMessage.value.trim() || !isConnected.value) return

  // 模拟发送消息
  const messageData = {
    id: Date.now(),
    senderName: props.user.username,
    content: newMessage.value.trim(),
    timestamp: new Date().toISOString()
  }

  addMessage(messageData)
  newMessage.value = ''
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  const timeStr = date.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  })
  
  if (messageDate.getTime() === today.getTime()) {
    return timeStr
  } else {
    return `${date.toLocaleDateString()} ${timeStr}`
  }
}

// 处理接收到的消息
const handleMessage = (data) => {
  switch (data.type) {
    case 3: // CHAT_MESSAGE
      addMessage(data)
      break
    case 4: // USER_LIST
      onlineUsers.value = data.users || []
      break
    case 5: // USER_JOIN
      addMessage({
        senderName: 'System',
        content: data.content,
        timestamp: data.timestamp,
        type: 'system'
      })
      break
    case 6: // USER_LEAVE
      addMessage({
        senderName: 'System',
        content: data.content,
        timestamp: data.timestamp,
        type: 'system'
      })
      break
    case 8: // HISTORY_RESPONSE
      if (data.messages && Array.isArray(data.messages)) {
        messages.value = [...data.messages]
        scrollToBottom()
      }
      break
    case 9: // ERROR_MESSAGE
      console.error('Server error:', data.error)
      break
  }
}

// 请求历史消息
const requestHistory = async () => {
  try {
    await sendMessage({
      type: 7, // HISTORY_REQUEST
      limit: 50
    })
  } catch (error) {
    console.error('Failed to request history:', error)
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 模拟初始化
  isConnected.value = true
  scrollToBottom()
})

// 组件卸载时清理
onUnmounted(() => {
  // 清理工作
})

// 监听用户变化
watch(() => props.user, (newUser) => {
  if (newUser) {
    // 用户信息更新时的处理
  }
})
</script>

<style scoped>
.chat-room {
  width: 100%;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.chat-container {
  display: flex;
  height: 600px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* 用户列表侧边栏 */
.users-sidebar {
  width: 250px;
  background: rgba(0, 0, 0, 0.05);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.02);
}

.sidebar-header h3 {
  margin: 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.users-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 0.25rem;
  transition: background-color 0.2s ease;
}

.user-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

.user-item.current-user {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  margin-right: 0.75rem;
}

.user-name {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.you-label {
  font-size: 0.8rem;
  color: #667eea;
  font-weight: 600;
}

/* 聊天主区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.message-item {
  max-width: 70%;
  align-self: flex-start;
}

.message-item.own-message {
  align-self: flex-end;
}

.message-item.system-message {
  align-self: center;
  max-width: 90%;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
}

.sender-name {
  font-weight: 600;
  color: #667eea;
}

.own-message .sender-name {
  color: #764ba2;
}

.system-message .sender-name {
  color: #666;
}

.message-time {
  color: #999;
  font-size: 0.75rem;
}

.message-content {
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 12px;
  word-wrap: break-word;
  line-height: 1.4;
}

.own-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.system-message .message-content {
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  font-style: italic;
  text-align: center;
}

/* 消息输入区域 */
.message-input-container {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem;
  background: rgba(0, 0, 0, 0.02);
}

.message-form {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.message-input {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 25px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.message-input:focus {
  border-color: #667eea;
}

.message-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.send-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.connection-status {
  text-align: center;
  font-size: 0.8rem;
  font-weight: 500;
}

.connection-status.connected {
  color: #28a745;
}

.connection-status.disconnected {
  color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    height: 500px;
  }
  
  .users-sidebar {
    width: 200px;
  }
  
  .message-item {
    max-width: 85%;
  }
}
</style>
