# TCP聊天室项目

一个基于C++后端和Vue.js前端的实时聊天室应用。

## 项目结构

```
ChatRoom/
├── chat-room-back-end/          # C++后端服务器
│   ├── include/                 # 头文件
│   │   ├── ChatServer.h        # 聊天服务器主类
│   │   ├── DbConnect.h         # 数据库连接类
│   │   ├── SimpleJson.h        # 简单JSON处理类
│   │   └── WebSocketServer.h   # WebSocket服务器类
│   ├── src/                    # 源文件
│   │   ├── ChatServer.cpp      # 聊天服务器实现
│   │   ├── DbConnect.cpp       # 数据库连接实现
│   │   ├── main.cpp           # 主程序入口
│   │   └── test.cpp           # 测试文件
│   ├── database_init.sql       # 数据库初始化脚本
│   └── chat-room-back-end.sln  # Visual Studio解决方案
├── chat-room-front-end/         # Vue.js前端
│   ├── src/
│   │   ├── components/         # Vue组件
│   │   │   ├── LoginForm.vue   # 登录表单组件
│   │   │   └── ChatRoom.vue    # 聊天室主界面
│   │   ├── api/               # API通信模块
│   │   │   ├── api.js         # HTTP API
│   │   │   └── websocket.js   # WebSocket通信
│   │   ├── App.vue            # 主应用组件
│   │   └── main.js            # 应用入口
│   ├── package.json           # 项目依赖
│   └── vite.config.js         # Vite配置
└── mysqlConnect/               # MySQL连接器
```

## 功能特性

### 后端功能
- ✅ TCP服务器监听和客户端连接管理
- ✅ 用户注册和登录认证
- ✅ 实时消息广播
- ✅ 消息历史记录存储
- ✅ 多线程客户端处理
- ✅ 数据库集成（MySQL）
- ✅ JSON消息协议
- 🔄 WebSocket协议支持（开发中）

### 前端功能
- ✅ 现代化Vue.js界面
- ✅ 用户登录/注册表单
- ✅ 实时聊天界面
- ✅ 在线用户列表
- ✅ 消息历史显示
- ✅ 响应式设计
- 🔄 WebSocket通信（开发中）

## 环境要求

### 后端要求
- Windows 10/11
- Visual Studio 2019/2022
- MySQL Server 8.0+
- MySQL Connector/C++ 9.4.0

### 前端要求
- Node.js 16+
- npm 或 yarn

## 安装和配置

### 1. 数据库设置

1. 安装MySQL Server
2. 创建数据库：
```sql
mysql -u root -p < chat-room-back-end/database_init.sql
```

3. 更新数据库连接配置（在 `DbConnect.cpp` 中）：
```cpp
this->host = "tcp://localhost:3306";
this->user = "root";
this->password = "your_password";
this->database = "public";
```

### 2. 后端设置

1. 打开Visual Studio
2. 加载 `chat-room-back-end/chat-room-back-end.sln`
3. 确保MySQL Connector/C++路径正确配置
4. 编译并运行项目

### 3. 前端设置

```bash
cd chat-room-front-end
npm install
npm run dev
```

## 运行项目

### 启动后端服务器
1. 在Visual Studio中运行C++项目
2. 服务器将在端口8080上启动
3. 控制台会显示连接状态和日志

### 启动前端应用
```bash
cd chat-room-front-end
npm run dev
```
前端将在 http://localhost:5173 上运行

## 使用说明

1. **注册账户**：首次使用需要注册新账户
2. **登录**：使用用户名和密码登录
3. **发送消息**：在输入框中输入消息并按回车或点击发送按钮
4. **查看历史**：登录后自动加载最近的聊天历史
5. **在线用户**：右侧显示当前在线用户列表

## 消息协议

系统使用JSON格式的消息协议：

```json
{
  "type": 1,           // 消息类型
  "username": "user1", // 用户名
  "content": "Hello",  // 消息内容
  "timestamp": "..."   // 时间戳
}
```

### 消息类型
- `1`: 登录
- `2`: 注册
- `3`: 聊天消息
- `4`: 用户列表
- `5`: 用户加入
- `6`: 用户离开
- `7`: 历史记录请求
- `8`: 历史记录响应
- `9`: 错误消息
- `10`: 心跳

## 开发状态

### 已完成
- [x] C++后端TCP服务器核心功能
- [x] 数据库集成和用户管理
- [x] Vue.js前端界面设计
- [x] 用户认证系统
- [x] 消息历史记录

### 进行中
- [ ] WebSocket协议实现
- [ ] 前后端通信集成
- [ ] 错误处理和重连机制

### 待完成
- [ ] 文件传输功能
- [ ] 私聊功能
- [ ] 用户头像
- [ ] 消息加密
- [ ] 移动端适配

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 验证连接参数是否正确
   - 确保数据库已创建

2. **编译错误**
   - 检查MySQL Connector/C++路径
   - 确保所有依赖库已安装
   - 验证Visual Studio版本兼容性

3. **前端连接失败**
   - 确保后端服务器正在运行
   - 检查端口是否被占用
   - 验证WebSocket连接地址

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License
