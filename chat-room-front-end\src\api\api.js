// HTTP API functions (if needed for REST endpoints)
const API_BASE_URL = 'http://localhost:8080'

export const api = {
  // 可以添加HTTP API调用，如果后端提供REST接口
  async get(endpoint) {
    const response = await fetch(`${API_BASE_URL}${endpoint}`)
    return response.json()
  },

  async post(endpoint, data) {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    return response.json()
  }
}