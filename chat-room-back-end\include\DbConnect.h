#pragma once
#include <iostream>
#include <memory>
#include <mysql/jdbc.h>
#include <string>
using namespace std;
using namespace sql;

class DbConnect {
private:
	string host;
	string user;
	string password;
	string database;
	shared_ptr<Connection> db;  // 使用shared_ptr替代unique_ptr以提高兼容性
public:
	DbConnect();
	~DbConnect();
	shared_ptr<ResultSet> GetRecords(string sql);
	shared_ptr<ResultSet> Get1Record(string sql);
};