@echo off
echo Building complete test...

REM Try to find Visual Studio installation
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if exist "%VSWHERE%" (
    for /f "usebackq tokens=*" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
        set "VS_PATH=%%i"
    )
)

if defined VS_PATH (
    echo Found Visual Studio at: %VS_PATH%
    call "%VS_PATH%\VC\Auxiliary\Build\vcvars64.bat"
    
    echo Compiling complete test with MSVC...
    cl /EHsc /std:c++17 /I"include" test_complete.cpp /Fe:test_complete.exe
    
    if %ERRORLEVEL% EQU 0 (
        echo Build successful! Running complete test...
        test_complete.exe
    ) else (
        echo Build failed
    )
) else (
    echo Visual Studio not found
)

pause
