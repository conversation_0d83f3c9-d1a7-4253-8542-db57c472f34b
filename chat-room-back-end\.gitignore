# 编译产物
*.o
*.obj
*.a
*.lib
*.so
*.dylib
*.dll
*.exe
*.out
*.app
*.dSYM/
*.vsidx
*.pdb
*.ilk
*.idb
*.ipch
*.log

# 编译目录
build/
cmake-build-*/
out/
bin/
lib/
chat-roo.*/
x64/
Debug/
Release/
*.pdb

# CMake 相关
CMakeCache.txt
CMakeFiles/
Makefile
cmake_install.cmake
install_manifest.txt
CTestTestfile.cmake

# 预编译头文件
*.gch
*.pch

# IDE 配置
.idea/
.vscode/
*.sublime-*
*.swp
*.swo
.DS_Store
*.suo
*.user
*.sln
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.cbp
*.workspace

# 日志和临时文件
*.log
*.tmp
*.temp
*.bak
*.swp
*.swo

# 配置文件（如果包含敏感信息）
*.ini
*.conf
*.cfg
!default.ini  # 如果有默认配置文件需要提交，可添加例外

# 第三方库（如果已通过包管理器管理）
libs/
external/
vendor/
!external/README.md  # 如果第三方库目录下有说明文件需要提交

# 测试相关
test/coverage/
test/reports/

# 文档生成目录
docs/html/
docs/latex/
.vs/