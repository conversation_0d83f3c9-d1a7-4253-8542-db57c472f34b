#pragma once
#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <thread>
#include <mutex>
#include <memory>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <sstream>
#include <regex>
#include "SimpleJson.h"

#pragma comment(lib, "ws2_32.lib")

using namespace std;

// WebSocket帧类型
enum class WebSocketOpcode {
    CONTINUATION = 0x0,
    TEXT = 0x1,
    BINARY = 0x2,
    CLOSE = 0x8,
    PING = 0x9,
    PONG = 0xA
};

// WebSocket帧结构
struct WebSocketFrame {
    bool fin;
    WebSocketOpcode opcode;
    bool masked;
    uint64_t payloadLength;
    uint32_t maskingKey;
    vector<uint8_t> payload;
};

// WebSocket客户端信息
struct WebSocketClient {
    SOCKET socket;
    string username;
    string userId;
    bool isAuthenticated;
    bool isWebSocketHandshakeComplete;
    thread clientThread;
    
    WebSocketClient(SOCKET s) : socket(s), isAuthenticated(false), isWebSocketHandshakeComplete(false) {}
};

class WebSocketServer {
private:
    SOCKET serverSocket;
    int port;
    bool isRunning;
    
    // 客户端管理
    map<SOCKET, shared_ptr<WebSocketClient>> clients;
    mutex clientsMutex;
    
    // 消息处理回调
    function<void(shared_ptr<WebSocketClient>, const SimpleJson&)> messageHandler;
    function<void(shared_ptr<WebSocketClient>)> connectHandler;
    function<void(shared_ptr<WebSocketClient>)> disconnectHandler;
    
    // 私有方法
    bool InitializeWinsock();
    bool CreateServerSocket();
    bool BindAndListen();
    void AcceptClients();
    void HandleClient(shared_ptr<WebSocketClient> client);
    
    // WebSocket协议处理
    bool PerformWebSocketHandshake(shared_ptr<WebSocketClient> client);
    string GenerateWebSocketAcceptKey(const string& key);
    WebSocketFrame ParseWebSocketFrame(const vector<uint8_t>& data);
    vector<uint8_t> CreateWebSocketFrame(const string& message, WebSocketOpcode opcode = WebSocketOpcode::TEXT);
    
    // 消息处理
    void ProcessWebSocketMessage(shared_ptr<WebSocketClient> client, const string& message);
    
    // 工具方法
    string Base64Encode(const vector<uint8_t>& data);
    vector<uint8_t> SHA1Hash(const string& input);
    
public:
    WebSocketServer(int serverPort = 8080);
    ~WebSocketServer();
    
    bool Start();
    void Stop();
    void Run();
    
    // 设置回调函数
    void SetMessageHandler(function<void(shared_ptr<WebSocketClient>, const SimpleJson&)> handler);
    void SetConnectHandler(function<void(shared_ptr<WebSocketClient>)> handler);
    void SetDisconnectHandler(function<void(shared_ptr<WebSocketClient>)> handler);
    
    // 发送消息
    void SendToClient(SOCKET clientSocket, const SimpleJson& message);
    void BroadcastMessage(const SimpleJson& message, SOCKET excludeSocket = INVALID_SOCKET);
    
    // 获取服务器状态
    bool IsRunning() const { return isRunning; }
    int GetClientCount() const;
    vector<string> GetConnectedUsers() const;
    void RemoveClient(SOCKET clientSocket);
};
