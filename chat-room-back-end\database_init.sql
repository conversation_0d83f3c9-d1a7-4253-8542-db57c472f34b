-- 聊天室数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS public CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE public;

-- 创建用户表
CREATE TABLE IF NOT EXISTS info_user (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_username (user_name),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建聊天消息表
CREATE TABLE IF NOT EXISTS chat_messages (
    message_id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id VARCHAR(20) NOT NULL,
    sender_name VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    message_type INT NOT NULL DEFAULT 3,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    INDEX idx_sender (sender_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_type (message_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户会话表（可选，用于管理在线状态）
CREATE TABLE IF NOT EXISTS user_sessions (
    session_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP NULL,
    ip_address VARCHAR(45),
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES info_user(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些测试用户数据
INSERT IGNORE INTO info_user (user_name, password, email) VALUES
('admin', 'admin123', '<EMAIL>'),
('user1', 'password1', '<EMAIL>'),
('user2', 'password2', '<EMAIL>'),
('testuser', 'test123', '<EMAIL>');

-- 插入一些测试消息数据
INSERT IGNORE INTO chat_messages (sender_id, sender_name, content, message_type) VALUES
('1', 'admin', 'Welcome to the chat room!', 3),
('2', 'user1', 'Hello everyone!', 3),
('3', 'user2', 'Nice to meet you all!', 3);

-- 创建视图：获取最近的聊天消息
CREATE OR REPLACE VIEW recent_messages AS
SELECT 
    m.message_id,
    m.sender_id,
    m.sender_name,
    m.content,
    m.timestamp,
    u.user_name
FROM chat_messages m
LEFT JOIN info_user u ON m.sender_id = u.user_id
WHERE m.is_deleted = FALSE
ORDER BY m.timestamp DESC
LIMIT 100;

-- 创建存储过程：清理旧消息（保留最近1000条）
DELIMITER //
CREATE PROCEDURE CleanOldMessages()
BEGIN
    DECLARE message_count INT;
    
    SELECT COUNT(*) INTO message_count FROM chat_messages WHERE is_deleted = FALSE;
    
    IF message_count > 1000 THEN
        UPDATE chat_messages 
        SET is_deleted = TRUE 
        WHERE message_id IN (
            SELECT message_id FROM (
                SELECT message_id 
                FROM chat_messages 
                WHERE is_deleted = FALSE 
                ORDER BY timestamp ASC 
                LIMIT (message_count - 1000)
            ) AS old_messages
        );
    END IF;
END //
DELIMITER ;

-- 显示创建的表
SHOW TABLES;

-- 显示用户表结构
DESCRIBE info_user;

-- 显示消息表结构
DESCRIBE chat_messages;

SELECT 'Database initialization completed successfully!' AS Status;
